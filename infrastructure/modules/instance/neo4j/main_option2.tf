###############################################
# Simplified Neo4j management with focused scripts #
###############################################

# Plan step: compute which databases must be created
data "external" "neo4j_plan" {
  program = ["python3", "${path.module}/scripts/neo4j_plan.py"]

  query = {
    api_base_url    = local.aura_api_base_url
    access_token    = local.access_token
    tenant_id       = local.neo4j_credentials.TENANT_ID
    neo4j_customers = jsonencode(var.neo4j_customers)
  }
}

locals {
  neo4j_to_create_list = try(jsondecode(data.external.neo4j_plan.result.to_create_json), [])
  neo4j_plan_hash      = try(data.external.neo4j_plan.result.to_create_hash, "")
  neo4j_results_path   = "${path.module}/.neo4j_created.json"
  db_to_secret_id      = { for c in var.neo4j_customers : c.database_name => "${c.tenant_name}_${c.facility_name}_AuraDB" }
}

# Apply step: create missing instances
resource "null_resource" "neo4j_create" {
  count = length(local.neo4j_to_create_list) > 0 ? 1 : 0

  triggers = {
    plan_hash = local.neo4j_plan_hash
  }

  provisioner "local-exec" {
    command = <<EOT
      echo '${jsonencode({
    api_base_url     = local.aura_api_base_url
    access_token     = local.access_token
    tenant_id        = local.neo4j_credentials.TENANT_ID
    neo4j_version    = var.neo4j_version
    aura_region      = var.aura_region
    instance_memory  = var.instance_memory
    instance_storage = var.instance_storage
    aura_type        = var.aura_type
    to_create        = local.neo4j_to_create_list
    results_path     = local.neo4j_results_path
})}' | python3 "${path.module}/scripts/neo4j_create.py"
    EOT
}
}

# Read created results
data "external" "neo4j_created" {
  program = ["python3", "${path.module}/scripts/neo4j_read.py"]

  query = {
    results_path = local.neo4j_results_path
  }

  depends_on = [null_resource.neo4j_create]
}

locals {
  neo4j_created_map = try(jsondecode(data.external.neo4j_created.result.created_map_json), {})

  # Secret data for all customers
  neo4j_secret_data = {
    for customer in var.neo4j_customers : customer.database_name => (
      lookup(local.neo4j_created_map, customer.database_name, null) != null ? {
        NEO4J_URI         = "neo4j+s://${local.neo4j_created_map[customer.database_name].instance_id}.databases.neo4j.io"
        NEO4J_USERNAME    = local.neo4j_created_map[customer.database_name].username
        NEO4J_PASSWORD    = local.neo4j_created_map[customer.database_name].password
        AURA_INSTANCEID   = local.neo4j_created_map[customer.database_name].instance_id
        AURA_INSTANCENAME = local.neo4j_created_map[customer.database_name].database_name
        } : {
        NEO4J_URI         = ""
        NEO4J_USERNAME    = ""
        NEO4J_PASSWORD    = ""
        AURA_INSTANCEID   = ""
        AURA_INSTANCENAME = customer.database_name
        STATUS            = "NOT_CREATED"
      }
    )
  }
}

# Create secrets for newly created instances
resource "google_secret_manager_secret" "neo4j_instances_created" {
  for_each = local.neo4j_created_map

  secret_id = local.db_to_secret_id[each.key]
  project   = var.project_id

  labels = var.common_resource_labels

  replication {
    auto {}
  }
}

resource "google_secret_manager_secret_version" "neo4j_instances_created" {
  for_each = local.neo4j_created_map

  secret      = google_secret_manager_secret.neo4j_instances_created[each.key].id
  secret_data = jsonencode(local.neo4j_secret_data[each.key])
}
