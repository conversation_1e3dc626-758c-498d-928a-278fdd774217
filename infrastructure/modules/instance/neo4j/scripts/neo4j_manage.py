#!/usr/bin/env python3
"""
Simplified Neo4j AuraDB management helper for Terraform.
<PERSON><PERSON> plan, apply, and read operations in a single focused script.
"""

import json
import sys
import os
import hashlib
import requests


def do_plan(inputs):
    """Get existing instances and determine what needs to be created."""
    api_base_url = inputs["api_base_url"]
    access_token = inputs["access_token"]
    tenant_id = inputs["tenant_id"]
    neo4j_customers = json.loads(inputs["neo4j_customers"])

    # Get existing instances
    response = requests.get(
        f"{api_base_url}/instances",
        headers={
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json",
        },
        params={"tenantId": tenant_id},
        timeout=30,
    )
    response.raise_for_status()

    # Parse existing instance names
    existing_instances = response.json().get("data", [])
    existing_names = {instance["name"] for instance in existing_instances}

    # Determine what needs to be created
    desired_names = {customer["database_name"] for customer in neo4j_customers}
    to_create = list(desired_names - existing_names)

    # Generate hash for change detection
    to_create_json = json.dumps(sorted(to_create))
    plan_hash = hashlib.sha256(to_create_json.encode()).hexdigest()[:16]

    return {"to_create_json": to_create_json, "to_create_hash": plan_hash}


def do_apply(inputs):
    """Create the specified Neo4j instances."""
    api_base_url = inputs["api_base_url"]
    access_token = inputs["access_token"]
    tenant_id = inputs["tenant_id"]
    to_create = inputs["to_create"]
    results_path = inputs["results_path"]

    # Instance configuration
    config = {
        "version": inputs["neo4j_version"],
        "region": inputs["aura_region"],
        "memory": inputs["instance_memory"],
        "storage": inputs["instance_storage"],
        "cloud_provider": "gcp",
        "type": inputs["aura_type"],
        "tenant_id": tenant_id,
    }

    created_instances = {}

    # Create each instance
    for database_name in to_create:
        instance_data = {**config, "name": database_name}

        response = requests.post(
            f"{api_base_url}/instances",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json",
                "Accept": "application/json",
            },
            json=instance_data,
            timeout=60,
        )
        response.raise_for_status()

        # Extract credentials from response
        data = response.json().get("data", {})
        created_instances[database_name] = {
            "instance_id": data.get("id"),
            "database_name": database_name,
            "username": data.get("username"),
            "password": data.get("password"),
        }

    # Save results to file
    with open(results_path, "w", encoding="utf-8") as f:
        json.dump(created_instances, f, indent=2)

    return {"status": "ok", "created_count": str(len(created_instances))}


def do_read_created(inputs):
    """Read previously created instance results from file."""
    results_path = inputs.get("results_path", "/tmp/neo4j_created.json")

    if os.path.exists(results_path):
        with open(results_path, "r", encoding="utf-8") as f:
            created_map = json.load(f)
    else:
        created_map = {}

    return {"created_map_json": json.dumps(created_map)}


def main():
    """Main entry point for the script."""
    try:
        inputs = json.load(sys.stdin)
        action = inputs.get("action", "plan")

        if action == "plan":
            result = do_plan(inputs)
        elif action == "apply":
            result = do_apply(inputs)
        elif action == "read_created":
            result = do_read_created(inputs)
        else:
            raise ValueError(f"Unknown action: {action}")

        json.dump(result, sys.stdout)

    except requests.RequestException as e:
        json.dump({"error": f"API request failed: {e}"}, sys.stdout)
        sys.exit(1)
    except Exception as e:
        json.dump({"error": f"Operation failed: {e}"}, sys.stdout)
        sys.exit(1)


if __name__ == "__main__":
    main()
