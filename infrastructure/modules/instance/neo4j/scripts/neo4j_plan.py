#!/usr/bin/env python3
"""
Simple Neo4j Aura planning script.
Compares desired vs existing instances and outputs what needs to be created.
"""

import json
import sys
import hashlib
import requests


def main():
    try:
        # Read input from stdin
        inputs = json.load(sys.stdin)
        
        # Extract required parameters
        api_base_url = inputs["api_base_url"]
        access_token = inputs["access_token"]
        tenant_id = inputs["tenant_id"]
        neo4j_customers = json.loads(inputs["neo4j_customers"])
        
        # Get existing instances
        response = requests.get(
            f"{api_base_url}/instances",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Accept": "application/json"
            },
            params={"tenantId": tenant_id},
            timeout=30
        )
        response.raise_for_status()
        
        # Parse existing instance names
        existing_instances = response.json().get("data", [])
        existing_names = {instance["name"] for instance in existing_instances}
        
        # Determine what needs to be created
        desired_names = {customer["database_name"] for customer in neo4j_customers}
        to_create = list(desired_names - existing_names)
        
        # Generate hash for change detection
        to_create_json = json.dumps(sorted(to_create))
        plan_hash = hashlib.sha256(to_create_json.encode()).hexdigest()[:16]
        
        # Output result
        result = {
            "to_create_json": to_create_json,
            "to_create_hash": plan_hash
        }
        json.dump(result, sys.stdout)
        
    except requests.RequestException as e:
        json.dump({"error": f"API request failed: {e}"}, sys.stdout)
        sys.exit(1)
    except Exception as e:
        json.dump({"error": f"Planning failed: {e}"}, sys.stdout)
        sys.exit(1)


if __name__ == "__main__":
    main()
