#!/usr/bin/env python3
"""
Simple script to read Neo4j instance creation results.
"""

import json
import sys
import os


def main():
    try:
        # Read input from stdin
        inputs = json.load(sys.stdin)
        results_path = inputs.get("results_path", "/tmp/neo4j_created.json")
        
        # Read results file if it exists
        if os.path.exists(results_path):
            with open(results_path, "r") as f:
                created_map = json.load(f)
        else:
            created_map = {}
        
        # Output result
        result = {"created_map_json": json.dumps(created_map)}
        json.dump(result, sys.stdout)
        
    except Exception as e:
        json.dump({"error": f"Read failed: {e}"}, sys.stdout)
        sys.exit(1)


if __name__ == "__main__":
    main()
