#!/usr/bin/env python3
"""
Simple Neo4j Aura instance creation script.
Creates the specified instances and saves credentials to a file.
"""

import json
import sys
import requests


def main():
    try:
        # Read input from stdin
        inputs = json.load(sys.stdin)
        
        # Extract required parameters
        api_base_url = inputs["api_base_url"]
        access_token = inputs["access_token"]
        tenant_id = inputs["tenant_id"]
        to_create = inputs["to_create"]
        results_path = inputs["results_path"]
        
        # Instance configuration
        config = {
            "version": inputs["neo4j_version"],
            "region": inputs["aura_region"],
            "memory": inputs["instance_memory"],
            "storage": inputs["instance_storage"],
            "cloud_provider": "gcp",
            "type": inputs["aura_type"],
            "tenant_id": tenant_id
        }
        
        created_instances = {}
        
        # Create each instance
        for database_name in to_create:
            instance_data = {**config, "name": database_name}
            
            response = requests.post(
                f"{api_base_url}/instances",
                headers={
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json",
                    "Accept": "application/json"
                },
                json=instance_data,
                timeout=60
            )
            response.raise_for_status()
            
            # Extract credentials from response
            data = response.json().get("data", {})
            created_instances[database_name] = {
                "instance_id": data.get("id"),
                "database_name": database_name,
                "username": data.get("username"),
                "password": data.get("password")
            }
        
        # Save results to file
        with open(results_path, "w") as f:
            json.dump(created_instances, f, indent=2)
        
        # Output success
        result = {
            "status": "ok",
            "created_count": str(len(created_instances))
        }
        json.dump(result, sys.stdout)
        
    except requests.RequestException as e:
        json.dump({"error": f"API request failed: {e}"}, sys.stdout)
        sys.exit(1)
    except Exception as e:
        json.dump({"error": f"Creation failed: {e}"}, sys.stdout)
        sys.exit(1)


if __name__ == "__main__":
    main()
