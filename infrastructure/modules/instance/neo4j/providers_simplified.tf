terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 6.38.0"
    }
    http = {
      source  = "hashicorp/http"
      version = "~> 3.0"
    }
    restapi = {
      source  = "Mastercard/restapi"
      version = "~> 1.18"
    }
  }
}

# Configure the REST API provider for Neo4j Aura
provider "restapi" {
  uri                  = local.aura_api_base_url
  write_returns_object = true

  headers = {
    Authorization = "Bearer ${local.access_token}"
    Content-Type  = "application/json"
    Accept        = "application/json"
  }
}
