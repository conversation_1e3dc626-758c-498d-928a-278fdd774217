###############################################
# Simplified Neo4j Aura instance management  #
###############################################

# Get existing Neo4j instances
data "http" "neo4j_instances" {
  url = "${local.aura_api_base_url}/instances?tenantId=${local.neo4j_credentials.TENANT_ID}"

  request_headers = {
    Authorization = "Bearer ${local.access_token}"
    Accept        = "application/json"
  }

  lifecycle {
    postcondition {
      condition     = self.status_code == 200
      error_message = "Failed to fetch Neo4j instances. Status: ${self.status_code}, Response: ${self.response_body}"
    }
  }
}

locals {
  # Parse existing instances
  existing_instances = try(jsondecode(data.http.neo4j_instances.response_body).data, [])
  existing_names     = toset([for instance in local.existing_instances : instance.name])

  # Determine which databases need to be created
  desired_names = toset([for customer in var.neo4j_customers : customer.database_name])
  to_create     = setsubtract(local.desired_names, local.existing_names)
}

# Create missing Neo4j instances
resource "restapi_object" "neo4j_instances" {
  for_each = local.to_create

  path = "/instances"
  data = jsonencode({
    name           = each.value
    version        = var.neo4j_version
    region         = var.aura_region
    memory         = var.instance_memory
    storage        = var.instance_storage
    cloud_provider = "gcp"
    type           = var.aura_type
    tenant_id      = local.neo4j_credentials.TENANT_ID
  })

  create_method = "POST"
  read_method   = "GET"
  read_path     = "/instances/{id}"
}

# Store credentials in GCP Secret Manager
resource "google_secret_manager_secret" "neo4j_instances" {
  for_each = local.to_create

  secret_id = local.db_to_secret_id[each.value]
  project   = var.project_id

  labels = var.common_resource_labels

  replication {
    auto {}
  }
}

resource "google_secret_manager_secret_version" "neo4j_instances" {
  for_each = local.to_create

  secret = google_secret_manager_secret.neo4j_instances[each.value].id
  secret_data = jsonencode({
    NEO4J_URI         = "neo4j+s://${restapi_object.neo4j_instances[each.value].api_data.id}.databases.neo4j.io"
    NEO4J_USERNAME    = restapi_object.neo4j_instances[each.value].api_data.username
    NEO4J_PASSWORD    = restapi_object.neo4j_instances[each.value].api_data.password
    AURA_INSTANCEID   = restapi_object.neo4j_instances[each.value].api_data.id
    AURA_INSTANCENAME = each.value
  })
}
